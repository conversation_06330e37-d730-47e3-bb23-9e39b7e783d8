{"llm": {"provider": "openai", "model": "gpt-4o", "apiKey": "${OPENAI_API_KEY}", "temperature": 0.1, "maxTokens": 4096}, "agent": {"maxSteps": 10, "timeout": 60000, "autoInitialize": true, "verbose": true}, "serverManager": {"enabled": true, "maxConcurrentServers": 3, "serverStartupTimeout": 30, "healthMonitoring": true, "healthCheckInterval": 30000, "autoReconnect": true}, "servers": [{"id": "playwright-mcp", "name": "Playwright MCP Server", "description": "Provides browser automation capabilities via Microsoft Playwright MCP", "connectionType": "stdio", "command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@microsoft/playwright-mcp", "--key", "${SMITHERY_API_KEY}"], "enabled": false, "priority": 10, "tags": ["browser", "automation", "playwright", "web"], "timeout": 45000, "retry": {"maxAttempts": 3, "delayMs": 2000, "backoffMultiplier": 2}}, {"id": "docfork-mcp", "name": "DocFork MCP Server", "description": "Provides documentation and library research capabilities via DocFork MCP", "connectionType": "http", "url": "https://server.smithery.ai/@docfork/mcp/mcp?api_key=${SMITHERY_API_KEY}&profile=${SMITHERY_PROFILE}", "preferSse": false, "enabled": true, "priority": 8, "tags": ["documentation", "research", "libraries", "docs"], "timeout": 30000, "retry": {"maxAttempts": 3, "delayMs": 1500, "backoffMultiplier": 2}}], "logging": {"level": "info", "format": "text"}}