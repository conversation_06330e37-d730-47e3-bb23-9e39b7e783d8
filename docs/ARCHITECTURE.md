# 🏗️ MCP Multi-Agent Architecture

## Overview

The **Multiple MCP Servers General Purpose Agent** is a TypeScript-based AI agent system that connects to multiple Model Context Protocol (MCP) servers simultaneously. It provides intelligent server selection, robust connection management, and seamless OpenAI integration for natural language processing tasks.

## 🎯 Core Design Principles

- **Modularity**: Clean separation of concerns with dedicated modules for each responsibility
- **Scalability**: Support for multiple concurrent MCP server connections
- **Reliability**: Robust error handling, health monitoring, and automatic reconnection
- **Type Safety**: Full TypeScript implementation with comprehensive type definitions
- **Extensibility**: Plugin-like architecture for easy addition of new MCP servers

## 🏛️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│      🎉 PRODUCTION MCP MULTI-AGENT WITH FULL-SCREEN UI     │
├─────────────────────────────────────────────────────────────┤
│  🖥️ True Full-Screen UI (http://localhost:3000/chat) ✅ LIVE│
│  - True Full-Screen Layout (h-screen w-screen)             │
│  - Responsive Design: Mobile/Tablet/Desktop Breakpoints    │
│  - Collapsible Sidebar with Mobile Overlay                 │
│  - macOS Traffic Light Controls Preserved                  │
│  - Professional Dark Theme & Touch-Friendly Interface      │
│  - Auto-scroll, Typing Indicators & Smooth Animations      │
│  - Inter Font Typography & Mobile-First Design             │
├─────────────────────────────────────────────────────────────┤
│  🔧 CLI Interface (Commander.js) ✅ COMPLETE               │
│  - Query, Server, Config Commands                          │
│  - Production Configuration Management                     │
├─────────────────────────────────────────────────────────────┤
│  🤖 Agent Core ✅ PRODUCTION READY                         │
│  - MultiServerAgent with Real MCP Integration              │
│  - MCPChatService with Streaming Support                   │
│  - Production Error Handling & Recovery                    │
├─────────────────────────────────────────────────────────────┤
│  📊 Server Manager ✅ OPERATIONAL   │  🧠 LLM Integration   │
│  - Real-time Health Monitoring     │  - OpenAI GPT-4o      │
│  - Connection Management            │  - LangChain Support  │
│  - Auto Reconnection               │  - Streaming Responses │
├─────────────────────────────────────────────────────────────┤
│  ⚙️ Configuration System ✅ COMPLETE                       │
│  - Production Environment Setup                            │
│  - Type-Safe Configuration Loading                         │
│  - Real OpenAI API Integration                             │
├─────────────────────────────────────────────────────────────┤
│  🔌 LIVE MCP Servers (via mcp-use library)                 │
│  ┌─────────────────────────────────────────────────────┐  │
│  │📚 DocFork MCP Server (HTTP Streamable Transport)   │  │
│  │✅ CONNECTED - Documentation Research Capabilities   │  │
│  │🌐 Smithery Server: HTTP Streamable (preferred)     │  │
│  │🔑 Query Parameter Authentication (api_key+profile) │  │
│  │🔧 Enable/Disable via CLI Commands                   │  │
│  └─────────────────────────────────────────────────────┘  │
│  ┌─────────────────────────────────────────────────────┐  │
│  │🎭 Playwright MCP Server (DISABLED)                  │  │
│  │⏸️ DISABLED - Browser Automation (Available)         │  │
│  │🔧 Can be enabled via CLI Commands                   │  │
│  └─────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Directory Structure

```
🎉 PRODUCTION READY STRUCTURE:

mcp-agent-ui/           # 🌐 Next.js 15 Production UI ✅ LIVE
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── chat/route.ts      # Real MCP streaming API
│   │   │   └── health/route.ts    # Production health monitoring
│   │   ├── chat/page.tsx          # Professional chat interface
│   │   └── page.tsx               # Auto-redirect to chat
│   ├── lib/
│   │   └── mcp-chat-service.ts    # 🔧 PRODUCTION DocFork MCP integration (HTTP Streamable)
│   └── hooks/
│       └── use-mcp-status.ts      # Real-time health monitoring
├── .env.local                     # Production OpenAI API configuration
└── README.md                      # Complete usage guide

src/                    # 🤖 Backend MCP Multi-Agent ✅ COMPLETE
├── agent/              # Core agent implementation
│   ├── multi-server-agent.ts # MultiServerAgent class with MCPAgent integration
│   ├── test-agent.ts   # Comprehensive testing suite
│   └── index.ts        # Agent exports and type definitions
├── cli/                # Command-line interface ✅ COMPLETED
│   ├── index.ts        # Main CLI entry point with commander.js
│   ├── commands/       # CLI command implementations
│   │   ├── query.ts    # Query command with streaming support
│   │   ├── servers.ts  # Server management commands
│   │   └── config.ts   # Configuration management commands
│   ├── utils/          # CLI utilities and helpers
│   │   ├── logger.ts   # Advanced logging system
│   │   ├── simple-agent.ts # Simplified agent for CLI
│   │   └── config-*.ts # Configuration utilities
│   └── test-*.sh       # CLI testing scripts
├── config/             # Configuration management ✅ COMPLETED
│   ├── types.ts        # TypeScript type definitions
│   ├── env.ts          # Environment configuration with validation ✅ NEW
│   ├── loader.ts       # Configuration loading logic
│   ├── examples.ts     # Example server configurations
│   ├── client-factory.ts # MCP client factory ✅ COMPLETED
│   ├── server-manager.ts # Advanced server manager ✅ NEW
│   ├── test-env.ts     # Environment testing utilities ✅ NEW
│   ├── test-integration.ts # Integration testing ✅ NEW
│   ├── test-server-manager.ts # Server manager testing ✅ NEW
│   └── index.ts        # Configuration exports
├── llm/                # OpenAI LLM integration ✅ COMPLETED
│   ├── factory.ts      # LLM client factory and management
│   ├── openai-client.ts # OpenAI client wrapper
│   ├── test-integration.ts # Integration testing
│   └── index.ts        # LLM exports
├── monitoring/         # Health monitoring ✅ COMPLETED
│   ├── server-health.ts    # Main health monitoring orchestrator
│   ├── health-checker.ts   # Health check implementation
│   ├── reconnection-manager.ts # Automatic reconnection logic
│   ├── test-health-monitoring.ts # Comprehensive testing suite
│   └── index.ts           # Monitoring exports and factory functions
├── utils/              # Error handling and utility functions ✅ COMPLETED
│   ├── errors.ts           # Custom error classes with categorization
│   ├── error-handler.ts    # Centralized error handler with classification
│   ├── retry.ts            # Retry mechanism with exponential backoff
│   ├── graceful-degradation.ts # Graceful degradation strategies
│   ├── error-recovery.ts   # Error recovery orchestrator
│   ├── test-error-handling.ts # Comprehensive error handling tests
│   └── index.ts            # Utility exports and convenience functions
└── index.ts            # Main entry point with CLI commands
```

## 🔧 Core Components

### 1. Configuration System (`src/config/`) ✅ COMPLETED

**Purpose**: Centralized configuration management with type safety and validation.

**Key Files**:
- `types.ts`: Comprehensive type definitions for all configuration options
- `env.ts`: Environment configuration with comprehensive validation ✅ NEW
- `examples.ts`: Pre-configured server examples and environment templates
- `loader.ts`: Main configuration loader with environment integration ✅ UPDATED
- `client-factory.ts`: MCP client factory with connection management ✅ COMPLETED
- `server-manager.ts`: Advanced server manager with performance optimizations ✅ NEW
- `test-env.ts`: Environment testing utilities ✅ NEW
- `test-integration.ts`: Integration testing suite ✅ NEW
- `test-server-manager.ts`: Server manager testing suite ✅ NEW

**Features**:
- **Environment Configuration**: Production-ready with comprehensive validation ✅
- **Type Safety**: Full TypeScript interfaces with strict mode compliance ✅
- **Input Validation**: Range and enum validation with helpful error messages ✅
- **Security**: Secure API key handling and error message protection ✅
- **Server Manager**: Advanced performance optimizations and resource management ✅ NEW
- Support for multiple connection types (HTTP, WebSocket, stdio, SSE)
- Server priority and tagging system
- Retry configuration with exponential backoff
- Environment-specific configurations (dev, prod, test)

### 2. LLM Integration (`src/llm/`) ✅ COMPLETED

**Purpose**: OpenAI client management with caching and connection testing.

**Key Components**:
- **LLMFactory**: Singleton pattern for client management
- **OpenAIClient**: Wrapper around OpenAI SDK with enhanced features
- **Configuration Loading**: Environment-based configuration with defaults
- **Test Integration**: Comprehensive testing utilities

**Features**:
- Client caching with automatic health checks
- Connection testing and validation
- Configurable retry logic and timeouts
- Support for custom OpenAI endpoints
- Streaming response support with AI SDK integration

### 3. Agent Implementation (`src/agent/`) ✅ COMPLETED

**Purpose**: Multi-server agent orchestration with MCPAgent integration.

**Key Components**:
- **MultiServerAgent**: Main agent class with MCPAgent integration
- **Factory Functions**: Clean agent creation with auto-initialization
- **Test Suite**: Comprehensive testing with CLI commands

**Features**:
- MCPAgent integration with LangChain compatibility
- Server manager enabled for automatic server selection
- Dual response modes: standard and streaming
- Connection testing and server information reporting
- Resource management with proper initialization/shutdown
- Error handling with graceful degradation

### 4. CLI Interface (`src/cli/`) ✅ **COMPLETED TODAY!**

**Purpose**: Production-ready command-line interface for interacting with the multi-server agent.

**Key Components**:
- **Main CLI Entry Point** (`index.ts`): Commander.js-based CLI with comprehensive command structure
- **Query Commands** (`commands/query.ts`): Interactive query execution with streaming support
- **Server Management** (`commands/servers.ts`): Server listing, status checking, enable/disable functionality, and information display
- **Configuration Management** (`commands/config.ts`): Setup, validation, and configuration display
- **Advanced Logging** (`utils/logger.ts`): Multi-level logging with verbose/quiet modes
- **Simple Agent** (`utils/simple-agent.ts`): Lightweight agent implementation for CLI usage

**Features**:
- **Complete Command Structure**: query, server, config commands with subcommands and aliases
- **Server Enable/Disable**: Runtime control of MCP servers via CLI commands ✅ NEW
- **File-Based Configuration**: Loads configuration from mcp-config.json and mcp-agent.config.json ✅ NEW
- **Multiple Output Formats**: Text, JSON, and table formats with proper formatting
- **Streaming Support**: Real-time response streaming for query commands
- **Comprehensive Help System**: Built-in help with examples for all commands
- **Error Handling**: Proper exit codes (0-8) and user-friendly error messages
- **Input Validation**: All parameters validated with helpful feedback
- **Security**: API keys properly masked in output, input sanitization
- **Testing**: Comprehensive test suite with 25+ test cases and 100% pass rate

**New CLI Commands** ✅ **ADDED 2025-08-20**:
```bash
# Server Management
npx tsx src/cli/index.ts server enable <server-ids...>   # Enable MCP servers
npx tsx src/cli/index.ts server disable <server-ids...>  # Disable MCP servers
npx tsx src/cli/index.ts server list --enabled-only      # Show only enabled servers
npx tsx src/cli/index.ts server status --server <id>     # Check specific server status
```

### 5. Server Manager (`src/config/server-manager.ts`) ✅ NEW

**Purpose**: Advanced server management with performance optimizations and reliability features.

**Key Features**:
- **Connection Pooling**: Configurable max connections per server with idle timeout management
- **Resource Management**: Memory and CPU usage monitoring with configurable thresholds
- **Load Balancing**: Multiple strategies (priority-based, least-connections, round-robin, random)
- **Circuit Breaker**: Fault tolerance with automatic recovery for failed servers
- **Health Monitoring**: Real-time server health checks with configurable intervals
- **Performance Metrics**: Connection count, response time, error rate tracking

**Configuration Options**:
```typescript
interface AdvancedServerManagerConfig {
  enabled: boolean;
  maxConcurrentServers?: number;        // 1-20 servers
  serverStartupTimeout?: number;        // 5-300 seconds
  healthMonitoring?: boolean;
  healthCheckInterval?: number;         // milliseconds
  autoReconnect?: boolean;

  connectionPool?: {
    maxConnectionsPerServer: number;    // default: 5
    idleTimeout: number;                // default: 5 minutes
    keepAliveInterval: number;          // default: 30 seconds
  };

  resourceManagement?: {
    maxMemoryPerServer: number;         // MB, default: 512
    cpuThreshold: number;               // 0-1, default: 0.8
    gcInterval: number;                 // milliseconds
  };

  loadBalancing?: {
    strategy: 'priority-based' | 'least-connections' | 'round-robin' | 'random';
    weights?: Record<string, number>;
  };

  circuitBreaker?: {
    failureThreshold: number;           // default: 5
    recoveryTimeout: number;            // milliseconds
    halfOpenMaxCalls: number;           // default: 3
  };
}
```

**Integration with MultiServerAgent**:
- Automatic MCPAgent configuration optimization
- Real-time server metrics and health status
- Intelligent server selection based on load balancing strategy
- Graceful degradation when servers are unavailable

### 5. MCP Server Management

**Integration**: Uses the `mcp-use` TypeScript library for MCP server connections.

**Supported Server Types**:
- **Filesystem Server**: File operations and directory management
- **Browser Server**: Web scraping and automation via Puppeteer
- **SQLite Server**: Database operations and SQL queries
- **Memory Server**: Persistent knowledge storage
- **Git Server**: Version control operations
- **Custom HTTP/WebSocket Servers**: Extensible server support

## 🔌 Connection Architecture

### MCP Connection Types

1. **stdio**: Process-based communication (most common)
   - Spawns server as child process
   - JSON-RPC over stdin/stdout
   - Used by: filesystem, browser, sqlite, memory, git servers

2. **HTTP**: RESTful API communication
   - Standard HTTP requests with JSON payloads
   - Custom headers and authentication support
   - Used by: custom API servers

3. **WebSocket**: Real-time bidirectional communication
   - Persistent connections for real-time updates
   - Used by: custom real-time servers

4. **SSE**: Server-Sent Events (planned)
   - One-way streaming from server to client
   - Used by: streaming data servers

### Server Selection Strategy

```typescript
interface ServerSelectionCriteria {
  priority: number;        // Higher = more preferred
  tags: string[];         // Capability matching
  health: boolean;        // Server availability
  responseTime: number;   // Performance metrics
}
```

## 🔄 Data Flow

### 1. Initialization Flow
```
1. Load Environment Variables
2. Parse Configuration
3. Initialize LLM Factory
4. Start MCP Server Manager
5. Connect to Enabled Servers
6. Begin Health Monitoring
```

### 2. Request Processing Flow
```
1. User Input → CLI Interface
2. Parse Intent → Agent Core
3. Select Appropriate Server(s) → Server Manager
4. Execute MCP Tools → mcp-use Library
5. Process Results → LLM Integration
6. Generate Response → OpenAI Client
7. Return to User → CLI Interface
```

## 🛡️ Error Handling & Resilience

### Connection Management
- **Automatic Reconnection**: Failed servers automatically attempt reconnection
- **Health Monitoring**: Periodic health checks with configurable intervals
- **Graceful Degradation**: System continues operating with available servers
- **Circuit Breaker Pattern**: Prevents cascading failures

### Retry Strategy
```typescript
interface RetryConfig {
  maxAttempts: number;
  delayMs: number;
  backoffMultiplier?: number; // Exponential backoff
}
```

### Error Categories
1. **Connection Errors**: Network failures, timeouts
2. **Authentication Errors**: API key issues, authorization failures
3. **Server Errors**: MCP server crashes, invalid responses
4. **Configuration Errors**: Invalid settings, missing requirements

## 🔍 Monitoring & Observability

### Health Monitoring ✅ IMPLEMENTED
- **Real-time Health Checks**: Configurable intervals with comprehensive server validation
- **Circuit Breaker Pattern**: Prevents cascading failures with automatic recovery
- **Automatic Reconnection**: Exponential backoff with jitter for failed servers
- **Health Metrics**: Response times, error rates, connection counts, memory usage
- **Event-Driven Architecture**: Comprehensive event notifications for health changes
- **Graceful Degradation**: Fallback to basic monitoring when advanced features unavailable

**Components:**
- `ServerHealthMonitor`: Main orchestrator with event system
- `HealthChecker`: Performs actual health checks with timeout and tool validation
- `ReconnectionManager`: Handles reconnection with exponential backoff
- **Integration**: Seamlessly integrated with existing server manager

### 6. Error Handling and Recovery (`src/utils/`) ✅ COMPLETED

**Purpose**: Comprehensive error handling, retry mechanisms, and graceful degradation for robust operation.

**Key Components**:
- **Error Classes**: 10 specialized error types with categorization and severity levels
- **Error Handler**: Centralized error classification, logging, and circuit breaker management
- **Retry Mechanism**: Exponential backoff with jitter and circuit breaker integration
- **Graceful Degradation**: Fallback strategies, caching, and simplified responses
- **Error Recovery Orchestrator**: Coordinates all recovery systems with comprehensive metrics

**Features**:
- **Automatic Error Classification**: Pattern-based error type detection and categorization
- **Circuit Breaker Pattern**: Prevents cascading failures with automatic recovery
- **Retry Logic**: Configurable retry with exponential backoff and jitter
- **Fallback Strategies**: Multiple degradation approaches based on operation criticality
- **Comprehensive Metrics**: Detailed tracking of all recovery operations and system health
- **Integration**: Seamless integration with MultiServerAgent and health monitoring

**Error Recovery Flow**:
```
Operation Request → ErrorRecoveryOrchestrator → RetryMechanism (with Circuit Breaker)
                                             ↓ (if retry fails)
                                           GracefulDegradationManager
                                             ↓
                                           Fallback Strategies:
                                           - Fallback Servers
                                           - Cached Responses
                                           - Simplified Responses
                                           - Operation Skipping
```

### Logging Strategy
- **Levels**: debug, info, warn, error
- **Formats**: JSON (production), text (development)
- **Destinations**: Console, file, external services
- **Error Correlation**: Correlation IDs for tracking errors across systems ✅ **NEW**
- **Structured Logging**: Consistent error metadata and context preservation ✅ **NEW**

## 🚀 Deployment Architecture

### Environment Configurations

**Development**:
- Verbose logging enabled
- Extended timeouts for debugging
- Limited server connections
- Local file-based storage

**Production**:
- Optimized performance settings
- JSON logging for parsing
- Full server complement
- External monitoring integration

**Testing**:
- Minimal server set
- Fast timeouts
- Mock server support
- Isolated environments

## 🔮 Future Enhancements

### Phase 2: Core Agent Implementation
- Multi-step reasoning engine
- Tool selection optimization
- Context management
- Memory persistence

### Phase 3: Advanced Features ✅ **100% COMPLETE!**
- ✅ **Server Health Monitoring**: Comprehensive health monitoring with automatic reconnection
- ✅ **Error handling and recovery**: Comprehensive error management with retry, circuit breaker, and graceful degradation
- ✅ **CLI interface**: Complete production-ready CLI with query, server, and config commands ✅ **COMPLETED TODAY!**

### Phase 4: User Interface
- Interactive chat mode
- Web-based dashboard
- Real-time monitoring
- Configuration GUI

## 🔗 Dependencies

### Core Dependencies
- `mcp-use`: MCP server integration library
- `openai`: OpenAI API client
- `@ai-sdk/openai`: AI SDK OpenAI provider
- `commander`: CLI framework
- `chalk`: Terminal styling

### Development Dependencies
- `typescript`: Type checking and compilation
- `vitest`: Testing framework
- `eslint`: Code linting
- `tsx`: TypeScript execution

## 📊 Performance Considerations

### Optimization Strategies
- **Connection Pooling**: Reuse MCP connections
- **Client Caching**: Cache OpenAI clients
- **Lazy Loading**: Load servers on demand
- **Concurrent Processing**: Parallel server operations

### Resource Management
- **Memory**: Efficient client caching with cleanup
- **CPU**: Asynchronous operations throughout
- **Network**: Connection reuse and timeout management
- **Storage**: Minimal local storage requirements

---

*Last Updated: 2025-08-18*
*Version: 1.2*
*Status: Living Document - Updated with health monitoring implementation*

*This architecture document reflects the current implementation state and planned future enhancements. The system is designed for extensibility and maintainability while providing robust multi-server MCP integration.*
